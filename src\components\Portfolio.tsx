import React from 'react';
import { FaInstagram } from 'react-icons/fa';
import { STUDIO_CONFIG } from '../config/constants';

const Portfolio: React.FC = () => {
  // Placeholder images - will be replaced with actual Instagram feed
  const placeholderImages = [
    'https://images.unsplash.com/photo-1519741497674-611481863552?w=500',
    'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=500',
    'https://images.unsplash.com/photo-1606216794079-e5b1e46f00c0?w=500',
    'https://images.unsplash.com/photo-1465495976277-4387d4b0b4c6?w=500',
    'https://images.unsplash.com/photo-1519167758481-83f29da8c2e6?w=500',
    'https://images.unsplash.com/photo-1529636798458-92182e662485?w=500',
    'https://images.unsplash.com/photo-1583939003579-730e3918a45a?w=500',
    'https://images.unsplash.com/photo-1522673607200-164d1b6ce486?w=500',
    'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=500',
  ];

  return (
    <section className="py-20 bg-white" id="portfolio">
      <div className="container mx-auto px-4 max-w-6xl">
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-4 text-primary font-heading">
          Our Portfolio
        </h2>
        <p className="text-center text-gray-600 mb-12">
          Follow us on Instagram to see our latest work
        </p>
        
        {/* Instagram Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
          {placeholderImages.map((image, index) => (
            <div 
              key={index} 
              className="relative group overflow-hidden rounded-lg aspect-square cursor-pointer"
            >
              <img
                src={image}
                alt={`Portfolio ${index + 1}`}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                <FaInstagram className="text-white text-4xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </div>
          ))}
        </div>
        
        {/* Instagram CTA */}
        <div className="text-center">
          <a
            href={STUDIO_CONFIG.social.instagram}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105"
          >
            <FaInstagram className="mr-2 text-2xl" />
            Follow Us on Instagram
          </a>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
