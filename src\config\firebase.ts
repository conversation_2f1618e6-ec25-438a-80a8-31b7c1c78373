import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Collect config from environment (Vite injects these at build time)
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// Helper to detect missing values and provide helpful console output
const missingKeys = Object.entries(firebaseConfig)
  .filter(([, v]) => !v)
  .map(([k]) => k);

if (missingKeys.length > 0) {
  console.warn(
    '[firebase] Missing Firebase config keys at runtime:',
    missingKeys,
    '\nIf you built the app locally, ensure `.env` contains all VITE_FIREBASE_* values before building.\n' +
      'If running the deployed site, rebuild/deploy with correct env values so the app has the right Firebase config.'
  );
}

let app: any = null;
let db: any = null;
let auth: any = null;

try {
  // Only attempt to initialize when a minimal set of config is present
  if (firebaseConfig.apiKey && firebaseConfig.projectId) {
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    auth = getAuth(app);
    console.info('[firebase] Initialized Firebase app for project:', firebaseConfig.projectId);
  } else {
    console.info('[firebase] Skipping Firebase init because required config is missing');
  }
} catch (err) {
  // Catch runtime init errors and print them for easier debugging on the client
  // eslint-disable-next-line no-console
  console.error('[firebase] Error initializing Firebase:', err);
}

export { db, auth };
export default app;
