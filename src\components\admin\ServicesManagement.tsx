import React, { useState, useEffect } from 'react';
import {
  collection,
  onSnapshot,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  query,
  orderBy,
  Timestamp,
} from 'firebase/firestore';
import { db } from '../../config/firebase';
import { Service } from '../../types';
import { FaPlus, FaEdit, FaTrash, FaToggleOn, FaToggleOff, FaArrowUp, FaArrowDown } from 'react-icons/fa';

const ServicesManagement: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    isBasePackage: false,
  });

  useEffect(() => {
    const q = query(collection(db, 'services'), orderBy('order', 'asc'));

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          servicesData.push({ id: doc.id, ...doc.data() } as Service);
        });

        setServices(servicesData);
        setLoading(false);
      },
      (err) => {
        console.error('Error listening to services:', err);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const handleAddService = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.price) {
      alert('Please fill in all fields');
      return;
    }

    try {
      const newService = {
        name: formData.name,
        price: Number(formData.price),
        isBasePackage: formData.isBasePackage,
        isActive: true,
        order: services.length,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };
      await addDoc(collection(db, 'services'), newService);

      setShowAddModal(false);
      setFormData({ name: '', price: '', isBasePackage: false });
    } catch (error) {
      console.error('Error adding service:', error);
      console.error('Error adding service:', error);
      alert('Failed to add service');
    }
  };

  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingService || !formData.name || !formData.price) {
      alert('Please fill in all fields');
      return;
    }

    try {
      const serviceRef = doc(db, 'services', editingService.id);
      await updateDoc(serviceRef, {
        name: formData.name,
        price: Number(formData.price),
        isBasePackage: formData.isBasePackage,
        updatedAt: Timestamp.now(),
      });

      setEditingService(null);
      setFormData({ name: '', price: '', isBasePackage: false });
    } catch (error) {
      console.error('Error updating service:', error);
      alert('Failed to update service');
    }
  };

  const handleDeleteService = async (serviceId: string) => {
    if (!confirm('Are you sure you want to delete this service?')) {
      return;
    }

    try {
      await deleteDoc(doc(db, 'services', serviceId));
    } catch (error) {
      console.error('Error deleting service:', error);
      alert('Failed to delete service');
    }
  };

  const toggleServiceActive = async (service: Service) => {
    try {
      const serviceRef = doc(db, 'services', service.id);
      await updateDoc(serviceRef, {
        isActive: !service.isActive,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error toggling service:', error);
      alert('Failed to toggle service status');
    }
  };

  const moveService = async (service: Service, direction: 'up' | 'down') => {
    const currentIndex = services.findIndex((s) => s.id === service.id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === services.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const otherService = services[newIndex];

    try {
      await updateDoc(doc(db, 'services', service.id), {
        order: newIndex,
        updatedAt: Timestamp.now(),
      });
      await updateDoc(doc(db, 'services', otherService.id), {
        order: currentIndex,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error reordering services:', error);
      alert('Failed to reorder services');
    }
  };

  const openEditModal = (service: Service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      price: service.price.toString(),
      isBasePackage: service.isBasePackage,
    });
  };

  if (loading) {
    return <div className="text-center py-8">Loading services...</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-primary">Services</h2>
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center bg-accent hover:bg-yellow-600 text-primary font-semibold px-4 py-2 rounded-lg transition-colors"
        >
          <FaPlus className="mr-2" />
          Add Service
        </button>
      </div>

      {/* Services Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price (PKR)
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {services.map((service, index) => (
              <tr key={service.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => moveService(service, 'up')}
                      disabled={index === 0}
                      className="text-gray-600 hover:text-accent disabled:text-gray-300"
                    >
                      <FaArrowUp />
                    </button>
                    <button
                      onClick={() => moveService(service, 'down')}
                      disabled={index === services.length - 1}
                      className="text-gray-600 hover:text-accent disabled:text-gray-300"
                    >
                      <FaArrowDown />
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{service.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">₨{service.price.toLocaleString()}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      service.isBasePackage
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {service.isBasePackage ? 'Base Package' : 'Add-on'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleServiceActive(service)}
                    className="flex items-center space-x-2"
                  >
                    {service.isActive ? (
                      <>
                        <FaToggleOn className="text-green-600 text-2xl" />
                        <span className="text-sm text-green-600">Active</span>
                      </>
                    ) : (
                      <>
                        <FaToggleOff className="text-gray-400 text-2xl" />
                        <span className="text-sm text-gray-400">Inactive</span>
                      </>
                    )}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => openEditModal(service)}
                    className="text-indigo-600 hover:text-indigo-900 mr-4"
                  >
                    <FaEdit />
                  </button>
                  <button
                    onClick={() => handleDeleteService(service.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <FaTrash />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add/Edit Modal */}
      {(showAddModal || editingService) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h3 className="text-2xl font-bold mb-4 text-primary">
              {editingService ? 'Edit Service' : 'Add New Service'}
            </h3>
            <form onSubmit={editingService ? handleUpdateService : handleAddService}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price (PKR)
                </label>
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-accent focus:outline-none"
                  required
                  min="0"
                />
              </div>
              <div className="mb-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isBasePackage}
                    onChange={(e) =>
                      setFormData({ ...formData, isBasePackage: e.target.checked })
                    }
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">This is the base package</span>
                </label>
              </div>
              <div className="flex space-x-4">
                <button
                  type="submit"
                  className="flex-1 bg-accent hover:bg-yellow-600 text-primary font-semibold py-2 px-4 rounded-lg transition-colors"
                >
                  {editingService ? 'Update' : 'Add'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false);
                    setEditingService(null);
                    setFormData({ name: '', price: '', isBasePackage: false });
                  }}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServicesManagement;
