import { Service } from '../types';
import { Timestamp } from 'firebase/firestore';

// Demo services for development/testing when Firebase is not configured
export const DEMO_SERVICES: Service[] = [
  {
    id: 'base-1',
    name: 'Base Package',
    price: 0,
    isBasePackage: true,
    isActive: true,
    order: 0,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-1',
    name: 'Videography',
    price: 8000,
    isBasePackage: false,
    isActive: true,
    order: 1,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-2',
    name: 'Drone Shots',
    price: 12000,
    isBasePackage: false,
    isActive: true,
    order: 2,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-3',
    name: 'Photo Album (Premium)',
    price: 5000,
    isBasePackage: false,
    isActive: true,
    order: 3,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-4',
    name: 'Extra Hours (per hour)',
    price: 3000,
    isBasePackage: false,
    isActive: true,
    order: 4,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-5',
    name: 'Professional Editing',
    price: 6000,
    isBasePackage: false,
    isActive: true,
    order: 5,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
  {
    id: 'addon-6',
    name: 'Same Day Delivery',
    price: 10000,
    isBasePackage: false,
    isActive: true,
    order: 6,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  },
];
