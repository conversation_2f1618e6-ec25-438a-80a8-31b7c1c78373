import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { STUDIO_CONFIG } from '../config/constants';

interface PrivateRouteProps {
  children: React.ReactNode;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const { currentUser } = useAuth();

  if (!currentUser) return <Navigate to="/admin/login" />;

  const email = currentUser.email || '';
  const isAdmin = STUDIO_CONFIG.adminEmails.length === 0 || STUDIO_CONFIG.adminEmails.includes(email);

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white p-8 rounded shadow text-center">
          <h2 className="text-2xl font-bold mb-2">Not authorized</h2>
          <p className="text-gray-600 mb-4">Your account does not have access to the admin panel.</p>
          <a href="/" className="text-primary underline">← Back to website</a>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default PrivateRoute;
