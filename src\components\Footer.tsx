import React from 'react';
import { FaInstagram, FaFacebook, FaWhatsapp, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import { STUDIO_CONFIG } from '../config/constants';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    section?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <footer className="bg-primary text-white pt-16 pb-8">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {/* About */}
          <div>
            <h3 className="text-2xl font-bold mb-4 text-accent font-heading">
              {STUDIO_CONFIG.name}
            </h3>
            <p className="text-gray-300 mb-4">
              {STUDIO_CONFIG.tagline}
            </p>
            <p className="text-gray-400 text-sm">
              Creating beautiful memories through professional photography and videography services.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-semibold mb-4 text-accent">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => scrollToSection('about')}
                  className="text-gray-300 hover:text-accent transition-colors"
                >
                  About Us
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('portfolio')}
                  className="text-gray-300 hover:text-accent transition-colors"
                >
                  Portfolio
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('services')}
                  className="text-gray-300 hover:text-accent transition-colors"
                >
                  Services & Pricing
                </button>
              </li>
              <li>
                <a
                  href="/admin"
                  className="text-gray-300 hover:text-accent transition-colors"
                >
                  Admin Panel
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-semibold mb-4 text-accent">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <FaPhone className="text-accent mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-300">{STUDIO_CONFIG.contact.phone}</span>
              </li>
              <li className="flex items-start">
                <FaEnvelope className="text-accent mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-300">{STUDIO_CONFIG.contact.email}</span>
              </li>
              <li className="flex items-start">
                <FaMapMarkerAlt className="text-accent mt-1 mr-3 flex-shrink-0" />
                <span className="text-gray-300">{STUDIO_CONFIG.contact.address}</span>
              </li>
            </ul>

            {/* Social Media */}
            <div className="flex space-x-4 mt-6">
              <a
                href={STUDIO_CONFIG.social.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="text-2xl text-gray-300 hover:text-accent transition-colors"
              >
                <FaInstagram />
              </a>
              <a
                href={STUDIO_CONFIG.social.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-2xl text-gray-300 hover:text-accent transition-colors"
              >
                <FaFacebook />
              </a>
              <a
                href={STUDIO_CONFIG.social.whatsappUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-2xl text-gray-300 hover:text-accent transition-colors"
              >
                <FaWhatsapp />
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-700 pt-8 text-center">
          <p className="text-gray-400">
            &copy; {currentYear} {STUDIO_CONFIG.name}. All rights reserved.
          </p>
          <p className="text-gray-500 text-sm mt-2">
            Designed with ❤️ for capturing perfect moments
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
