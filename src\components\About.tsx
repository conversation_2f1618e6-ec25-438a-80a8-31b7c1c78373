import React from 'react';
import { STUDIO_CONFIG } from '../config/constants';
import { FaCheckCircle } from 'react-icons/fa';

const About: React.FC = () => {
  return (
    <section className="py-20 bg-gray-50" id="about">
      <div className="container mx-auto px-4 max-w-6xl">
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-12 text-primary font-heading">
          {STUDIO_CONFIG.about.title}
        </h2>
        
        <div className="grid md:grid-cols-2 gap-12 mb-12">
          <div>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {STUDIO_CONFIG.about.description}
            </p>
            
            <div className="mb-6">
              <h3 className="text-2xl font-semibold text-primary mb-3 font-heading">Our Mission</h3>
              <p className="text-gray-700">{STUDIO_CONFIG.about.mission}</p>
            </div>
            
            <div>
              <h3 className="text-2xl font-semibold text-primary mb-3 font-heading">Our Vision</h3>
              <p className="text-gray-700">{STUDIO_CONFIG.about.vision}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-semibold text-primary mb-6 font-heading">
              Why Choose Us?
            </h3>
            <ul className="space-y-4">
              {STUDIO_CONFIG.about.differentiators.map((item, index) => (
                <li key={index} className="flex items-start">
                  <FaCheckCircle className="text-accent text-xl mr-3 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
