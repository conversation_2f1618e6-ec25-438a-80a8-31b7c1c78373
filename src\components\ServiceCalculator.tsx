import React, { useState, useEffect } from 'react';
import { collection, query, where, onSnapshot, orderBy, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { Service } from '../types';
import { FaWhatsapp, FaCheckSquare, FaRegSquare } from 'react-icons/fa';
import { STUDIO_CONFIG } from '../config/constants';
import {
  generateUniqueQuoteId,
  saveQuote,
  formatWhatsAppMessage,
  generateWhatsAppUrl,
} from '../utils/quoteUtils';
import { DEMO_SERVICES } from '../data/demoServices';

// Check if Firebase is configured by verifying multiple env vars
const isFirebaseConfigured = () => {
  const required = [
    import.meta.env.VITE_FIREBASE_API_KEY,
    import.meta.env.VITE_FIREBASE_PROJECT_ID,
    import.meta.env.VITE_FIREBASE_APP_ID,
  ];
  return required.every((v) => typeof v === 'string' && v.length > 0 && !v.includes('your_'));
};

// Use demo mode when Firebase is not configured
const USE_DEMO_MODE = !isFirebaseConfigured();

const ServiceCalculator: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [indexBuilding, setIndexBuilding] = useState(false);
  const [indexRetryCount, setIndexRetryCount] = useState(0);
  const [clientName, setClientName] = useState('');
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  

  // Debug helper: enable by adding ?debug=true to the URL
  const showDebug = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('debug') === 'true';

  // Manual one-off refresh (useful when index just finished building)
  const manualRefresh = async () => {
    if (USE_DEMO_MODE) return;
    setLoading(true);
    setError(null);
    try {
      const q = query(collection(db, 'services'), where('isActive', '==', true), orderBy('order', 'asc'));
      const snap = await getDocs(q);
      const servicesData: Service[] = [];
      snap.forEach((doc) => servicesData.push({ id: doc.id, ...doc.data() } as Service));
      setServices(servicesData);
      setError(null);
    } catch (e: any) {
      setError(`Manual refresh failed: ${e?.message || e}`);
      setErrorDetails(JSON.stringify({ message: e?.message, code: e?.code }, null, 2));
    } finally {
      setLoading(false);
    }
  };

  // Fetch services from Firestore with real-time updates OR use demo data
  useEffect(() => {
    

    if (USE_DEMO_MODE) {
      // Use demo data when Firebase is not configured
      console.log('🎭 Demo Mode: Using local demo data');
      setServices(DEMO_SERVICES);
      setLoading(false);
      return;
    }

    // Use real Firebase data
    const q = query(
      collection(db, 'services'),
      where('isActive', '==', true),
      orderBy('order', 'asc')
    );

    let unsub = () => {};
    let retryTimer: any = null;

    const startSnapshot = () => {
      unsub = onSnapshot(
        q,
        (snapshot) => {
            const servicesData: Service[] = [];
            snapshot.forEach((doc) => {
              servicesData.push({ id: doc.id, ...doc.data() } as Service);
            });

            setServices(servicesData);
          setLoading(false);
          setError(null);
          setErrorDetails(null);
          setIndexBuilding(false);
          if (retryTimer) {
            clearTimeout(retryTimer);
            retryTimer = null;
          }
        },
        (err) => {
          console.error('Error fetching services:', err);
          const e: any = err;
          const short = e?.message || e?.code || String(err);
          setError(`Failed to load services: ${short}`);
          try {
            const payload = {
              message: e?.message,
              code: e?.code,
              stack: e?.stack,
            };
            setErrorDetails(JSON.stringify(payload, null, 2));
          } catch (ee) {
            setErrorDetails(String(err));
          }

          // Detect index-related errors and start polling to retry
          const msg = (e?.message || '').toLowerCase();
          if (msg.includes('requires an index') || msg.includes('index is currently building') || msg.includes('index')) {
            setIndexBuilding(true);
            setIndexRetryCount(0);

            const maxAttempts = 12; // try for up to ~2 minutes (12 * 10s)
            const attemptIntervalMs = 10000; // 10s

            const attempt = async () => {
              setIndexRetryCount((c) => c + 1);
              try {
                const snap = await getDocs(q);
                const servicesData: Service[] = [];
                snap.forEach((doc) => servicesData.push({ id: doc.id, ...doc.data() } as Service));
                setServices(servicesData);
                setLoading(false);
                setError(null);
                setErrorDetails(null);
                setIndexBuilding(false);
              } catch (retryErr) {
                const re: any = retryErr;
                console.log('Index not ready yet, retrying...', re?.message || retryErr);
                setIndexRetryCount((c) => c + 1);
                if (indexRetryCount < maxAttempts) {
                  retryTimer = setTimeout(attempt, attemptIntervalMs);
                } else {
                  setIndexBuilding(false);
                }
              }
            };

            // start first attempt after a short delay
            retryTimer = setTimeout(attempt, attemptIntervalMs);
          }

          setLoading(false);
        }
      );
    };

    startSnapshot();

    return () => {
      try { unsub(); } catch (e) {}
      if (retryTimer) clearTimeout(retryTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const basePackage = services.find((s) => s.isBasePackage);
  const addOnServices = services.filter((s) => !s.isBasePackage);

  const toggleService = (serviceId: string) => {
    setSelectedServiceIds((prev) =>
      prev.includes(serviceId)
        ? prev.filter((id) => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const calculateTotal = (): number => {
    let total = basePackage?.price || 0;
    
    selectedServiceIds.forEach((id) => {
      const service = services.find((s) => s.id === id);
      if (service) {
        total += service.price;
      }
    });
    
    return total;
  };

  const handleWhatsAppCheckout = async () => {
    if (!clientName.trim()) {
      alert('Please enter your name');
      return;
    }

    if (!basePackage) {
      alert('Base package not available');
      return;
    }

    setIsProcessing(true);

    try {
      // Generate unique Quote ID
      const quoteId = USE_DEMO_MODE 
        ? `QT-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-DEMO`
        : await generateUniqueQuoteId();

      // Prepare selected services
      const selectedServices = [
        {
          serviceId: basePackage.id,
          name: basePackage.name,
          price: basePackage.price,
        },
        ...selectedServiceIds.map((id) => {
          const service = services.find((s) => s.id === id)!;
          return {
            serviceId: service.id,
            name: service.name,
            price: service.price,
          };
        }),
      ];

      const totalPrice = calculateTotal();

      // Save quote to Firestore (skip in demo mode)
      if (!USE_DEMO_MODE) {
        const savedRemotely = await saveQuote(quoteId, clientName, selectedServices, totalPrice);
        if (!savedRemotely) {
          // If quote wasn't saved remotely, caller will receive a failure; keep previous behavior of alerting
        }
      } else {
        console.log('🎭 Demo Mode: Quote not saved to Firebase', { quoteId, clientName, totalPrice });
      }

      // Format WhatsApp message
      const message = formatWhatsAppMessage(
        clientName,
        selectedServices,
        totalPrice,
        quoteId
      );

      // Generate WhatsApp URL
      const whatsappUrl = generateWhatsAppUrl(
        STUDIO_CONFIG.contact.whatsapp,
        message
      );

      // Show success message
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);

      // Open WhatsApp
      window.open(whatsappUrl, '_blank');

      // Reset form
      setClientName('');
      setSelectedServiceIds([]);
    } catch (err) {
      console.error('Error creating quote:', err);
      alert('Failed to create quote. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (loading) {
    return (
      <section className="py-20 bg-gray-50" id="services">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-accent"></div>
            <p className="mt-4 text-gray-600">Loading services...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20 bg-gray-50" id="services">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <div className="font-semibold">{error}</div>
            {errorDetails && (
              <details className="mt-2 text-sm text-gray-700">
                <summary className="cursor-pointer">Show error details</summary>
                <pre className="whitespace-pre-wrap mt-2 text-xs">{errorDetails}</pre>
              </details>
            )}
          </div>
        </div>
      </section>
    );
  }

  const totalPrice = calculateTotal();

  return (
    <section className="py-20 bg-gray-50" id="services">
      <div className="container mx-auto px-4 max-w-4xl">
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-4 text-primary font-heading">
          Our Services & Pricing
        </h2>
        <p className="text-center text-gray-600 mb-12">
          Customize your package and get an instant quote
        </p>

        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Base Package */}
          {basePackage ? (
            <div className="mb-8 p-6 bg-gradient-to-r from-primary to-secondary rounded-lg text-white">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold mb-2">{basePackage.name}</h3>
                  <p className="text-gray-200">Included in every package</p>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-accent">
                    ₨{basePackage.price.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-300">PKR</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="mb-6">
              <div className="bg-yellow-50 border border-yellow-300 text-yellow-800 px-4 py-3 rounded">
                <p className="font-semibold">Base package not available</p>
                <p className="mt-1 text-sm text-yellow-700">
                  The site currently has no base package configured. The administrator can add one via the Admin panel. You can go to the <a href="/admin" className="underline">Admin Panel</a> and add a service with <code>isBasePackage</code> set to true and marked active.
                </p>
              </div>
            </div>
          )}

          {/* Add-on Services */}
          {addOnServices.length > 0 && (
            <div className="mb-8">
              <h3 className="text-xl font-semibold mb-4 text-primary">
                Add-on Services
              </h3>
              <div className="space-y-3">
                {addOnServices.map((service) => (
                  <div
                    key={service.id}
                    onClick={() => toggleService(service.id)}
                    className="flex items-center justify-between p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:border-accent"
                    style={{
                      borderColor: selectedServiceIds.includes(service.id)
                        ? '#d4af37'
                        : '#e5e7eb',
                      backgroundColor: selectedServiceIds.includes(service.id)
                        ? '#fffbf0'
                        : 'white',
                    }}
                  >
                    <div className="flex items-center">
                      {selectedServiceIds.includes(service.id) ? (
                        <FaCheckSquare className="text-accent text-2xl mr-3" />
                      ) : (
                        <FaRegSquare className="text-gray-400 text-2xl mr-3" />
                      )}
                      <span className="font-medium text-gray-800">
                        {service.name}
                      </span>
                    </div>
                    <span className="font-semibold text-primary">
                      ₨{service.price.toLocaleString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Client Name Input */}
          <div className="mb-6">
            <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-2">
              Your Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="clientName"
              value={clientName}
              onChange={(e) => setClientName(e.target.value)}
              placeholder="Enter your name"
              className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-accent focus:outline-none transition-colors"
              required
            />
          </div>

          {/* Total Price */}
          <div className="mb-6 p-6 bg-gray-100 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-xl font-semibold text-gray-700">
                Total Price:
              </span>
              <span className="text-4xl font-bold text-accent">
                ₨{totalPrice.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Success Message */}
          {showSuccess && (
            <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
              Quote created successfully! Opening WhatsApp...
            </div>
          )}
          {/* No local save notice in production */}

          {/* WhatsApp Checkout Button */}
          <button
            onClick={handleWhatsAppCheckout}
            disabled={!clientName.trim() || isProcessing || !basePackage}
            className="w-full bg-whatsapp hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg text-lg"
          >
            <FaWhatsapp className="text-2xl mr-2" />
            {isProcessing ? 'Processing...' : 'Get Quote on WhatsApp'}
          </button>

          <p className="text-center text-sm text-gray-500 mt-4">
            Click to send your customized quote via WhatsApp
          </p>
          {showDebug && (
            <div className="mt-4 p-4 bg-gray-100 rounded text-sm">
              <div className="mb-2">Debug:</div>
              <div>Demo mode: {USE_DEMO_MODE ? 'yes' : 'no'}</div>
              <div>Firebase initialized: {db ? 'yes' : 'no'}</div>
              <div>Services loaded: {services.length}</div>
              <div>Index building: {indexBuilding ? 'yes' : 'no'}</div>
              <div>Retry count: {indexRetryCount}</div>
              <div className="mt-2">
                <button onClick={manualRefresh} className="px-3 py-1 bg-primary text-white rounded">Manual refresh</button>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ServiceCalculator;
