import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { FaSignOutAlt, FaBox, FaFileInvoice } from 'react-icons/fa';
import ServicesManagement from '../components/admin/ServicesManagement';
import QuotesManagement from '../components/admin/QuotesManagement';
import { STUDIO_CONFIG } from '../config/constants';

type TabType = 'services' | 'quotes';

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('services');
  const { logout, currentUser } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/admin/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-primary text-white shadow-lg">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold font-heading">{STUDIO_CONFIG.name}</h1>
              <p className="text-sm text-gray-300">Admin Dashboard</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-300">{currentUser?.email}</span>
              <button
                onClick={handleLogout}
                className="flex items-center bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors"
              >
                <FaSignOutAlt className="mr-2" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white shadow">
        <div className="container mx-auto px-4">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('services')}
              className={`py-4 px-2 border-b-2 font-medium transition-colors flex items-center ${
                activeTab === 'services'
                  ? 'border-accent text-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <FaBox className="mr-2" />
              Services Management
            </button>
            <button
              onClick={() => setActiveTab('quotes')}
              className={`py-4 px-2 border-b-2 font-medium transition-colors flex items-center ${
                activeTab === 'quotes'
                  ? 'border-accent text-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <FaFileInvoice className="mr-2" />
              Quotes Management
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <main className="container mx-auto px-4 py-8">
        {activeTab === 'services' && <ServicesManagement />}
        {activeTab === 'quotes' && <QuotesManagement />}
      </main>
    </div>
  );
};

export default AdminDashboard;
