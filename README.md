# Studio Services Website

A production-ready, single-page business website for studio services (photography/videography) built with React, TypeScript, Tailwind CSS, and Firebase.

## Features

### Customer-Facing Features
- 🎨 Modern, responsive design (mobile, tablet, desktop)
- **Color Scheme**: Navy/Gold (easily customizable in `tailwind.config.js`)
- **Fonts**: <PERSON><PERSON><PERSON> + Montserrat from Google Fonts
- **Base Package Price**: ₨0 PKR (visible immediately)
- **Instagram Portfolio**: 9 placeholder images in grid layoution
- 💰 Dynamic service selector and price calculator
- 📱 WhatsApp checkout with unique Quote ID generation
- ⚡ Real-time price updates from Firebase

### Admin Panel Features
- 🔐 Secure Firebase Authentication
- 📦 Complete CRUD operations for services
- 🔄 Real-time service management (add, edit, delete, reorder, toggle active/inactive)
- 📊 Quotes management dashboard
- 🔍 Search and filter quotes
- 📈 Update quote status (pending → contacted → completed)
- 💾 Export quotes to CSV

## Tech Stack

- **Frontend:** React 18 + TypeScript
- **Build Tool:** Vite
- **Styling:** Tailwind CSS
- **Routing:** React Router v6
- **Backend:** Firebase (Firestore + Authentication)
- **Icons:** React Icons
- **Utilities:** date-fns, uuid

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Firebase account

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
cd studio-services-website
npm install
```

### 2. Firebase Project Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project (or use an existing one)
3. Enable **Firestore Database**:
   - Go to Build → Firestore Database
   - Click "Create database"
   - Start in **production mode**
   - Choose your region
4. Enable **Authentication**:
   - Go to Build → Authentication
   - Click "Get started"
   - Enable **Email/Password** sign-in method

### 3. Get Firebase Configuration

1. In Firebase Console, go to Project Settings (⚙️ icon)
2. Scroll down to "Your apps" section
3. Click the web icon (</>) to add a web app
4. Register your app (give it a nickname)
5. Copy the Firebase configuration object

### 4. Configure Environment Variables

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and paste your Firebase configuration:
   ```env
   VITE_FIREBASE_API_KEY=your_api_key_here
   VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   
   # Studio Configuration
   VITE_WHATSAPP_PHONE=+92XXXXXXXXXX
   VITE_INSTAGRAM_USERNAME=yourstudio
   ```

### 5. Deploy Firestore Security Rules

1. Install Firebase CLI:
   ```bash
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

3. Initialize Firebase in your project:
   ```bash
   firebase init firestore
   ```
   - Select your Firebase project
   - Accept the default `firestore.rules` file
   - Accept the default `firestore.indexes.json` file

4. Deploy the security rules:
   ```bash
   firebase deploy --only firestore:rules
   ```

### 6. Create First Admin User

Since there's no public registration, you need to create the first admin user manually:

1. Go to Firebase Console → Authentication → Users
2. Click "Add user"
3. Enter email and password (e.g., `<EMAIL>`)
4. Click "Add user"

### 7. Add Initial Services (Optional)

You can add services through the admin panel, or manually through Firebase Console:

1. Go to Firestore Database
2. Create a collection named `services`
3. Add documents with this structure:
   ```javascript
   {
     name: "Base Package",
     price: 10000,
     isBasePackage: true,
     isActive: true,
     order: 0,
     createdAt: <timestamp>,
     updatedAt: <timestamp>
   }
   ```

### 8. Run the Development Server

```bash
npm run dev
```

The application will open at `http://localhost:3000`

## Customization Guide

### Branding & Content

All branding elements can be customized in `src/config/constants.ts`:

```typescript
export const STUDIO_CONFIG = {
  name: 'Your Studio Name',
  tagline: 'Your Tagline',
  logoPath: '/logo.png',
  
  colors: {
    primary: '#1a1a2e',    // Dark blue-black
    secondary: '#16213e',  // Navy
    accent: '#d4af37',     // Gold
  },
  
  contact: {
    phone: '+92 XXX XXXXXXX',
    email: '<EMAIL>',
    address: 'Your Address',
  },
  
  social: {
    instagram: 'https://www.instagram.com/yourhandle',
    facebook: 'https://www.facebook.com/yourpage',
  },
  
  about: {
    description: 'Your studio description...',
    mission: 'Your mission...',
    vision: 'Your vision...',
    differentiators: [
      'Point 1',
      'Point 2',
      // Add more...
    ],
  },
};
```

### Colors

To change the color scheme:

1. Update `src/config/constants.ts` (for reference)
2. Update `tailwind.config.js`:
   ```javascript
   theme: {
     extend: {
       colors: {
         primary: '#YOUR_COLOR',
         secondary: '#YOUR_COLOR',
         accent: '#YOUR_COLOR',
       },
     },
   }
   ```

### Logo

1. Place your logo image in the `public` folder (e.g., `public/logo.png`)
2. Update `logoPath` in `src/config/constants.ts`
3. Replace the placeholder logo in `src/components/Hero.tsx`

### Background Images

Update the hero background image in `src/config/constants.ts`:
```typescript
hero: {
  backgroundImage: 'your-image-url-or-path',
}
```

## Building for Production

```bash
npm run build
```

The production-ready files will be in the `dist` folder.

## Deployment

### Deploy to Firebase Hosting

1. Initialize Firebase Hosting:
   ```bash
   firebase init hosting
   ```
   - Choose your Firebase project
   - Set public directory to: `dist`
   - Configure as single-page app: `Yes`
   - Set up automatic builds with GitHub: `No` (or `Yes` if you want)

2. Build the project:
   ```bash
   npm run build
   ```

3. Deploy:
   ```bash
   firebase deploy --only hosting
   ```

### Deploy to Other Platforms

The `dist` folder can be deployed to:
- **Vercel:** Connect your GitHub repo or use Vercel CLI
- **Netlify:** Drag and drop the `dist` folder or connect GitHub
- **Any static hosting:** Upload the `dist` folder

## Usage Guide

### For Customers

1. Visit the website
2. Browse services and portfolio
3. Scroll to "Services & Pricing" section
4. Base package (₨0) is automatically included
5. Select additional services by clicking checkboxes
6. Total price updates in real-time
7. Enter your name
8. Click "Get Quote on WhatsApp"
9. Quote is saved to database and WhatsApp opens with pre-filled message

### For Admins

1. Navigate to `/admin/login`
2. Login with admin credentials
3. **Services Management:**
   - Add new services with name and price
   - Mark services as base package or add-on
   - Edit existing services
   - Toggle services active/inactive
   - Reorder services with up/down arrows
   - Delete services (with confirmation)
4. **Quotes Management:**
   - View all customer quotes
   - Search by Quote ID or client name
   - Filter by status
   - Update quote status (pending/contacted/completed)
   - View full quote details
   - Export all quotes to CSV

## Database Structure

### Services Collection (`/services`)
```javascript
{
  id: "auto-generated",
  name: "Service Name",
  price: 5000,
  isBasePackage: false,
  isActive: true,
  order: 1,
  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

### Quotes Collection (`/quotes`)
```javascript
{
  id: "auto-generated",
  quoteId: "QT-20250109-A3F7",
  clientName: "John Doe",
  selectedServices: [
    { serviceId: "xxx", name: "Base Package", price: 10000 },
    { serviceId: "yyy", name: "Videography", price: 5000 }
  ],
  totalPrice: 15000,
  timestamp: Timestamp,
  status: "pending" | "contacted" | "completed"
}
```

## Security

- Firebase Authentication protects admin routes
- Firestore security rules restrict write access to authenticated users
- Environment variables keep Firebase config secure
- Quote IDs are unique and checked for duplicates
- Client-side validation prevents empty submissions

## Troubleshooting

### Firebase Connection Issues
- Verify `.env` file has correct Firebase configuration
- Check Firebase project is active and billing is enabled (if required)
- Ensure Firestore and Authentication are enabled in Firebase Console

### Services Not Loading
- Check Firestore security rules are deployed
- Verify services exist in Firestore database
- Check browser console for errors

### WhatsApp Not Opening
- Verify `VITE_WHATSAPP_PHONE` is set correctly in `.env`
- Phone number format: `+923001234567` (no spaces or dashes)
- Some browsers may block popup windows

### Admin Login Not Working
- Verify user exists in Firebase Authentication
- Check email and password are correct
- Ensure Authentication is enabled in Firebase Console

## Performance Optimization

- Images are lazy-loaded
- Firestore real-time listeners automatically clean up
- Components are optimized with React.memo where appropriate
- Tailwind CSS purges unused styles in production

## Browser Support

- Chrome/Edge (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Mobile browsers (iOS Safari, Chrome Mobile)

## License

This project is private and proprietary.

## Support

For issues or questions, contact your development team.

---

**Built with ❤️ for Studio Services**
