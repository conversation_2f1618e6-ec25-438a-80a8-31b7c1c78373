rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Services collection
    match /services/{serviceId} {
      // Anyone can read services
      allow read: if true;
      
      // Only authenticated admins can write
      allow write: if request.auth != null;
    }
    
    // Quotes collection
    match /quotes/{quoteId} {
      // Anyone can create quotes (customers)
      allow create: if true;
      
      // Only authenticated admins can read and update
      allow read, update: if request.auth != null;
      
      // No one can delete quotes
      allow delete: if false;
    }
  }
}
