import React from 'react';
import { STUDIO_CONFIG } from '../config/constants';

const Hero: React.FC = () => {
  const scrollToServices = () => {
    const servicesSection = document.getElementById('services');
    servicesSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section 
      className="relative h-screen flex items-center justify-center bg-cover bg-center"
      style={{ backgroundImage: `url(${STUDIO_CONFIG.hero.backgroundImage})` }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-60"></div>
      
      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl">
        <div className="mb-6">
          {/* Logo placeholder - replace with actual logo */}
          <div className="w-24 h-24 mx-auto mb-4 bg-accent rounded-full flex items-center justify-center">
            <span className="text-4xl font-bold text-primary">SE</span>
          </div>
        </div>
        
        <h1 className="text-5xl md:text-7xl font-bold mb-4 font-heading">
          {STUDIO_CONFIG.name}
        </h1>
        
        <p className="text-xl md:text-2xl mb-8 text-gray-200">
          {STUDIO_CONFIG.tagline}
        </p>
        
        <button
          onClick={scrollToServices}
          className="bg-accent hover:bg-yellow-600 text-primary font-semibold px-8 py-4 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          {STUDIO_CONFIG.hero.ctaText}
        </button>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>
  );
};

export default Hero;
