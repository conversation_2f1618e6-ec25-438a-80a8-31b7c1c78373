import { v4 as uuidv4 } from 'uuid';
import { format } from 'date-fns';
import { collection, addDoc, getDocs, query, where, Timestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import { Quote, SelectedService } from '../types';

// Production: no local fallback by default

/**
 * Generate a unique Quote ID in format: QT-YYYYMMDD-XXXX
 * Example: QT-20250109-A3F7
 */
export const generateQuoteId = (): string => {
  const dateStr = format(new Date(), 'yyyyMMdd');
  const randomStr = uuidv4().substring(0, 4).toUpperCase();
  return `QT-${dateStr}-${randomStr}`;
};

/**
 * Check if a Quote ID already exists in Firestore
 */
export const quoteIdExists = async (quoteId: string): Promise<boolean> => {
  const q = query(collection(db, 'quotes'), where('quoteId', '==', quoteId));
  const snapshot = await getDocs(q);
  return !snapshot.empty;
};

/**
 * Generate a unique Quote ID that doesn't exist in the database
 */
export const generateUniqueQuoteId = async (): Promise<string> => {
  let quoteId = generateQuoteId();
  let attempts = 0;
  const maxAttempts = 10;

  while (await quoteIdExists(quoteId) && attempts < maxAttempts) {
    quoteId = generateQuoteId();
    attempts++;
  }

  if (attempts === maxAttempts) {
    throw new Error('Failed to generate unique Quote ID');
  }

  return quoteId;
};

/**
 * Save a quote to Firestore
 */
export const saveQuote = async (
  quoteId: string,
  clientName: string,
  selectedServices: SelectedService[],
  totalPrice: number
): Promise<void> => {
  const quote: Omit<Quote, 'id'> = {
    quoteId,
    clientName,
    selectedServices,
    totalPrice,
    timestamp: Timestamp.now(),
    status: 'pending',
  };
  await addDoc(collection(db, 'quotes'), quote);
};

/**
 * Attempt to push any locally stored quotes to Firestore. Returns number of migrated quotes.
 */
// No local migration in production

/**
 * Format WhatsApp message with quote details
 */
export const formatWhatsAppMessage = (
  clientName: string,
  selectedServices: SelectedService[],
  totalPrice: number,
  quoteId: string
): string => {
  let message = `Hi, my name is ${clientName}.\n\n`;
  message += `I want this package:\n`;

  selectedServices.forEach((service) => {
    message += `${service.name} - ₨${service.price.toLocaleString()}\n`;
  });

  message += `\nTotal Price: ₨${totalPrice.toLocaleString()}\n`;
  message += `Quote ID: ${quoteId}`;

  return message;
};

/**
 * Generate WhatsApp URL with pre-filled message
 */
export const generateWhatsAppUrl = (
  phoneNumber: string,
  message: string
): string => {
  const cleanPhone = phoneNumber.replace(/\D/g, '');
  const encodedMessage = encodeURIComponent(message);
  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
};
