import { Timestamp } from 'firebase/firestore';

export interface Service {
  id: string;
  name: string;
  price: number;
  isBasePackage: boolean;
  isActive: boolean;
  order: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SelectedService {
  serviceId: string;
  name: string;
  price: number;
}

export interface Quote {
  id?: string;
  quoteId: string;
  clientName: string;
  selectedServices: SelectedService[];
  totalPrice: number;
  timestamp: Timestamp;
  status: 'pending' | 'contacted' | 'completed';
}

export interface QuoteFormData {
  clientName: string;
  selectedServiceIds: string[];
}
