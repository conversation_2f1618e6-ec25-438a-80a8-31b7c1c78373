// Studio Configuration - Easy Customization Point
export const STUDIO_CONFIG = {
  // Branding
  name: 'Studio Elite',
  tagline: 'Capturing Your Perfect Moments',
  logoPath: '/logo.png', // Place your logo in the public folder
  
  // Colors (used in Tailwind config)
  colors: {
    primary: '#1a1a2e',
    secondary: '#16213e',
    accent: '#d4af37',
  },
  
  // Contact Information
  contact: {
    phone: '+92 300 1234567',
    email: '<EMAIL>',
    address: '123 Photography Lane, Karachi, Pakistan',
    whatsapp: import.meta.env.VITE_WHATSAPP_PHONE || '+923001234567',
  },
  
  // Social Media
  social: {
    instagram: `https://www.instagram.com/${import.meta.env.VITE_INSTAGRAM_USERNAME || 'yourstudio'}`,
    facebook: 'https://www.facebook.com/yourstudio',
    whatsappUrl: `https://wa.me/${(import.meta.env.VITE_WHATSAPP_PHONE || '+923001234567').replace(/\+/g, '')}`,
  },
  
  // About Section
  about: {
    title: 'About Our Studio',
    description: `We are a premium photography and videography studio dedicated to capturing your most precious moments. 
    With years of experience and a passion for visual storytelling, we transform ordinary events into extraordinary memories.`,
    mission: 'To deliver exceptional visual experiences that exceed expectations.',
    vision: 'To be the most trusted name in professional photography and videography services.',
    differentiators: [
      'Professional team with 10+ years of experience',
      'State-of-the-art equipment and technology',
      'Customized packages tailored to your needs',
      'Quick turnaround time with premium quality',
      'Comprehensive coverage from start to finish',
    ],
  },

  // Admins - list of emails allowed to access the admin panel.
  // Add your admin emails here or set via environment and rebuild.
  adminEmails: (import.meta.env.VITE_ADMIN_EMAILS || '').split(',').map((s: string) => s.trim()).filter(Boolean) as string[],
  
  // Hero Section
  hero: {
    backgroundImage: 'https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?q=80&w=2071',
    ctaText: 'Explore Our Services',
  },
};
